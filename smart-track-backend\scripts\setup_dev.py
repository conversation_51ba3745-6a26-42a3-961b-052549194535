#!/usr/bin/env python3
"""
Development environment setup script for Smart Track backend
Sets up the complete development environment with Docker services and local backend
"""

import os
import sys
import subprocess
import time
import logging
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_command(command: str, description: str, cwd=None, check=True):
    """Run a command and handle errors"""
    logger.info(f"Running: {description}")
    try:
        result = subprocess.run(
            command.split() if isinstance(command, str) else command,
            capture_output=True,
            text=True,
            cwd=cwd or Path(__file__).parent.parent,
            check=check
        )
        
        if result.returncode == 0:
            logger.info(f"✅ {description} completed successfully")
            if result.stdout and result.stdout.strip():
                print(result.stdout)
        else:
            logger.error(f"❌ {description} failed")
            if result.stderr:
                print(result.stderr)
            return False
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed with exit code {e.returncode}")
        if e.stderr:
            print(e.stderr)
        return False
    except Exception as e:
        logger.error(f"❌ Error running {description}: {e}")
        return False


def check_prerequisites():
    """Check if required tools are installed"""
    logger.info("Checking prerequisites...")
    
    tools = [
        ("docker", "Docker"),
        ("docker-compose", "Docker Compose"),
        ("uv", "UV package manager")
    ]
    
    missing_tools = []
    for tool, name in tools:
        try:
            subprocess.run([tool, "--version"], capture_output=True, check=True)
            logger.info(f"✅ {name} is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error(f"❌ {name} is not installed")
            missing_tools.append(name)
    
    if missing_tools:
        logger.error(f"Please install missing tools: {', '.join(missing_tools)}")
        return False
    
    return True


def setup_python_environment():
    """Set up Python virtual environment with UV"""
    logger.info("Setting up Python environment with UV...")
    
    # Sync dependencies
    if not run_command("uv sync", "Installing Python dependencies"):
        return False
    
    logger.info("✅ Python environment setup completed")
    return True


def start_docker_services():
    """Start Docker services (PostgreSQL and Redis)"""
    logger.info("Starting Docker services...")
    
    # Stop any existing containers
    run_command("docker-compose down", "Stopping existing containers", check=False)
    
    # Start services
    if not run_command("docker-compose up -d postgres redis", "Starting PostgreSQL and Redis"):
        return False
    
    # Wait for services to be ready
    logger.info("Waiting for services to be ready...")
    time.sleep(10)
    
    # Check if services are healthy
    for service in ["postgres", "redis"]:
        if not run_command(f"docker-compose ps {service}", f"Checking {service} status"):
            return False
    
    logger.info("✅ Docker services started successfully")
    return True


def setup_database():
    """Initialize database with migrations"""
    logger.info("Setting up database...")
    
    # Wait a bit more for PostgreSQL to be fully ready
    time.sleep(5)
    
    # Create initial migration if none exists
    migrations_dir = Path(__file__).parent.parent / "migrations" / "versions"
    if not any(migrations_dir.glob("*.py")):
        logger.info("Creating initial migration...")
        if not run_command("uv run python scripts/manage_db.py migrate 'Initial migration: Create all tables'", 
                          "Creating initial migration"):
            return False
    
    # Apply migrations
    if not run_command("uv run python scripts/manage_db.py upgrade", "Applying migrations"):
        return False
    
    # Initialize database with sample data
    if not run_command("uv run python scripts/manage_db.py init", "Initializing database"):
        return False
    
    logger.info("✅ Database setup completed")
    return True


def create_env_file():
    """Create .env file if it doesn't exist"""
    env_file = Path(__file__).parent.parent / ".env"
    
    if env_file.exists():
        logger.info("✅ .env file already exists")
        return True
    
    logger.info("Creating .env file...")
    
    env_content = """# Smart Track Backend Environment Configuration
# Database
DATABASE_URL=postgresql://smart_track:password@localhost:5432/smart_track_db
TEST_DATABASE_URL=postgresql://smart_track:password@localhost:5432/smart_track_test_db

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT Authentication
SECRET_KEY=your-secret-key-here-change-in-production-$(openssl rand -hex 32)
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# CORS
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8081"]

# Environment
ENVIRONMENT=development
DEBUG=true
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        logger.info("✅ .env file created")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to create .env file: {e}")
        return False


def show_next_steps():
    """Show next steps to the user"""
    logger.info("\n🎉 Development environment setup completed!")
    print("\n" + "="*60)
    print("NEXT STEPS:")
    print("="*60)
    print("1. Start the backend server:")
    print("   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print()
    print("2. Access the API documentation:")
    print("   http://localhost:8000/docs")
    print()
    print("3. Test the API:")
    print("   curl http://localhost:8000/health")
    print()
    print("4. Manage database:")
    print("   uv run python scripts/manage_db.py --help")
    print()
    print("5. Stop Docker services when done:")
    print("   docker-compose down")
    print("="*60)


def main():
    """Main setup function"""
    logger.info("🚀 Setting up Smart Track development environment...")
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    steps = [
        ("Checking prerequisites", check_prerequisites),
        ("Creating environment file", create_env_file),
        ("Setting up Python environment", setup_python_environment),
        ("Starting Docker services", start_docker_services),
        ("Setting up database", setup_database),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 Step: {step_name}")
        if not step_func():
            logger.error(f"❌ Setup failed at step: {step_name}")
            sys.exit(1)
    
    show_next_steps()


if __name__ == "__main__":
    main()
