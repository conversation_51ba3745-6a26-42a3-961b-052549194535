[project]
name = "smart-track-backend"
version = "0.1.0"
description = "FastAPI backend for Smart Track mobile application - a comprehensive sales force automation system"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.4",
    "bcrypt>=4.3.0",
    "celery>=5.5.3",
    "fastapi>=0.116.1",
    "httpx>=0.28.1",
    "passlib[bcrypt]>=1.7.4",
    "pillow>=11.3.0",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.20",
    "redis>=6.2.0",
    "sqlalchemy>=2.0.42",
    "structlog>=25.4.0",
    "uvicorn[standard]>=0.35.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
]

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py313"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.mypy]
python_version = "3.13"
check_untyped_defs = true
disallow_any_generics = true
disallow_untyped_defs = true
follow_imports = "silent"
strict_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
disallow_any_unimported = true
no_implicit_optional = true
warn_return_any = true
warn_unused_configs = true
exclude = [
    "migrations/",
]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
