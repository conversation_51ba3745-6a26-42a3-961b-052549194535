[project]
name = "smart-track-backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.4",
    "celery>=5.5.3",
    "fastapi>=0.116.1",
    "httpx>=0.28.1",
    "pillow>=11.3.0",
    "pip>=25.2",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "python-multipart>=0.0.20",
    "redis>=6.2.0",
    "sqlalchemy>=2.0.42",
    "structlog>=25.4.0",
    "uvicorn>=0.35.0",
]
