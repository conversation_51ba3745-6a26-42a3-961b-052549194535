#!/bin/sh
'''exec' '/mnt/c/Users/<USER>/Downloads/Work/smart track app/Sales/smart-track-backend/.venv/bin/python3' "$0" "$@"
' '''
# -*- coding: utf-8 -*-
import sys
from pip._internal.cli.main import main
if __name__ == "__main__":
    if sys.argv[0].endswith("-script.pyw"):
        sys.argv[0] = sys.argv[0][:-11]
    elif sys.argv[0].endswith(".exe"):
        sys.argv[0] = sys.argv[0][:-4]
    sys.exit(main())
