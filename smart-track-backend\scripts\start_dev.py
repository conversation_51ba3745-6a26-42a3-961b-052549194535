#!/usr/bin/env python3
"""
Development server startup script for Smart Track backend
Starts the FastAPI server with hot reload for development
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_services():
    """Check if required services are running"""
    logger.info("Checking required services...")
    
    services = [
        ("postgres", "PostgreSQL database"),
        ("redis", "Redis cache")
    ]
    
    for service, description in services:
        try:
            result = subprocess.run(
                ["docker-compose", "ps", service],
                capture_output=True,
                text=True,
                cwd=Path(__file__).parent.parent
            )
            
            if "Up" in result.stdout:
                logger.info(f"✅ {description} is running")
            else:
                logger.warning(f"⚠️  {description} is not running")
                logger.info("Starting Docker services...")
                subprocess.run(
                    ["docker-compose", "up", "-d", "postgres", "redis"],
                    cwd=Path(__file__).parent.parent
                )
                break
        except Exception as e:
            logger.error(f"❌ Error checking {description}: {e}")
            return False
    
    return True


def start_server():
    """Start the FastAPI development server"""
    logger.info("Starting Smart Track backend server...")
    
    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # Start the server with UV
    try:
        subprocess.run([
            "uv", "run", "uvicorn", "app.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ], check=True)
    except KeyboardInterrupt:
        logger.info("\n👋 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Error starting server: {e}")
        sys.exit(1)


def main():
    """Main function"""
    logger.info("🚀 Starting Smart Track development server...")
    
    if not check_services():
        logger.error("❌ Required services are not available")
        sys.exit(1)
    
    start_server()


if __name__ == "__main__":
    main()
