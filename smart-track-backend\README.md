# Smart Track Backend

FastAPI backend for the Smart Track mobile application - a comprehensive sales force automation system.

## Features

- **User Management**: Authentication, authorization, and user profiles with role-based access control
- **Organizational Structure**: Multi-level hierarchy (Region → Branch → Territory)
- **Customer Management**: Comprehensive customer database with contact information and location data
- **Visit Management**: Plan, track, and manage customer visits with GPS tracking
- **Location Services**: GPS-based check-in/check-out and location validation
- **Photo Management**: Upload and manage visit photos with metadata
- **Visit Notes**: Categorized notes with priority levels
- **Route Optimization**: Intelligent visit route planning
- **Reporting**: Visit statistics and performance metrics

## Tech Stack

- **Framework**: FastAPI 0.116+
- **Database**: PostgreSQL 15
- **ORM**: SQLAlchemy 2.0+
- **Migrations**: Alembic
- **Authentication**: JWT tokens with refresh token support
- **File Storage**: Local file system with configurable upload directory
- **Caching**: Redis 7
- **Task Queue**: Celery 5.5+
- **Package Management**: UV (modern Python package manager)
- **API Documentation**: Swagger/OpenAPI with automatic generation

## Quick Start

### Prerequisites

- Python 3.13+
- [UV package manager](https://github.com/astral-sh/uv) (recommended)
- Docker & Docker Compose
- PostgreSQL 15+ (via Docker)
- Redis 7+ (via Docker)

### Automated Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smart-track-backend
   ```

2. **Run the automated setup**
   ```bash
   python scripts/setup_dev.py
   ```

   This script will:
   - Check prerequisites
   - Set up Python environment with UV
   - Start Docker services (PostgreSQL & Redis)
   - Create and apply database migrations
   - Initialize database with sample data
   - Create environment configuration

3. **Start the development server**
   ```bash
   python scripts/start_dev.py
   ```

   Or manually:
   ```bash
   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Manual Setup

If you prefer manual setup or need more control:

1. **Install UV package manager**
   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. **Set up Python environment**
   ```bash
   uv sync
   ```

3. **Start Docker services**
   ```bash
   docker-compose up -d postgres redis
   ```

4. **Set up database**
   ```bash
   # Create initial migration
   uv run python scripts/manage_db.py migrate "Initial migration: Create all tables"

   # Apply migrations
   uv run python scripts/manage_db.py upgrade

   # Initialize with sample data
   uv run python scripts/manage_db.py init
   ```

5. **Start the server**
   ```bash
   uv run uvicorn app.main:app --reload
   ```

## API Documentation

Once the server is running:

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **Health Check**: `http://localhost:8000/health`

## Development Workflow

### Package Management with UV

UV is a fast Python package manager that replaces pip and virtual environments:

```bash
# Install dependencies
uv sync

# Add new dependency
uv add package-name

# Add development dependency
uv add --dev package-name

# Remove dependency
uv remove package-name

# Run commands in the virtual environment
uv run python script.py
uv run pytest
```

### Database Management

Use the database management script for all database operations:

```bash
# Show all available commands
uv run python scripts/manage_db.py --help

# Create new migration
uv run python scripts/manage_db.py migrate "Description of changes"

# Apply migrations
uv run python scripts/manage_db.py upgrade

# Rollback migrations
uv run python scripts/manage_db.py downgrade <revision>

# Show migration history
uv run python scripts/manage_db.py history

# Show current revision
uv run python scripts/manage_db.py current

# Reset database (WARNING: deletes all data)
uv run python scripts/manage_db.py reset

# Initialize database without sample data
uv run python scripts/manage_db.py init --no-sample-data
```

### Running Tests

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app

# Run specific test file
uv run pytest tests/test_auth.py
```

### Code Quality

```bash
# Format code
uv run black app/

# Lint code
uv run ruff check app/

# Type checking
uv run mypy app/
```

## Project Structure

```
smart-track-backend/
├── app/
│   ├── api/v1/           # API routes and endpoints
│   │   ├── endpoints/    # Individual endpoint modules
│   │   └── api.py        # API router configuration
│   ├── core/             # Core functionality
│   │   ├── config.py     # Configuration settings
│   │   ├── database.py   # Database connection
│   │   └── security.py   # Authentication & security
│   ├── db/               # Database utilities
│   │   └── init_db.py    # Database initialization
│   ├── models/           # SQLAlchemy models
│   │   ├── user.py       # User and role models
│   │   ├── organization.py # Organizational structure
│   │   ├── customer.py   # Customer models
│   │   └── visit.py      # Visit management models
│   ├── schemas/          # Pydantic schemas
│   └── main.py           # FastAPI application
├── migrations/           # Alembic migrations
├── scripts/              # Development and management scripts
├── tests/                # Test files
├── uploads/              # File upload directory
├── docker-compose.yml    # Docker services configuration
├── pyproject.toml        # UV project configuration
└── README.md             # This file
```

## Database Schema

The application uses a comprehensive database schema with the following main entities:

### User Management
- **users**: Core user accounts with authentication
- **roles**: Role-based access control (admin, sales_manager, sales_representative)
- **permissions**: Granular permissions for system actions
- **role_permissions**: Many-to-many relationship between roles and permissions
- **user_profiles**: Extended user information and organizational hierarchy

### Organizational Structure
- **regions**: Top-level geographic divisions
- **branches**: Regional offices or branches
- **territories**: Sales territories within branches

### Customer Management
- **customers**: Customer database with location and business information
- **customer_contacts**: Contact persons for each customer

### Visit Management
- **visits**: Visit planning and tracking with GPS coordinates
- **visit_photos**: Photo attachments for visits
- **visit_notes**: Categorized notes and observations
- **visit_routes**: Route optimization and planning

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout

### Users
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update current user profile

### Customers
- `GET /api/v1/customers/` - List customers
- `POST /api/v1/customers/` - Create customer
- `GET /api/v1/customers/{id}` - Get customer details
- `PUT /api/v1/customers/{id}` - Update customer
- `GET /api/v1/customers/nearby` - Find nearby customers

### Visits
- `GET /api/v1/visits/` - List visits
- `POST /api/v1/visits/` - Create visit
- `GET /api/v1/visits/{id}` - Get visit details
- `PUT /api/v1/visits/{id}` - Update visit
- `POST /api/v1/visits/{id}/checkin` - Check in to visit
- `POST /api/v1/visits/{id}/checkout` - Check out from visit
- `POST /api/v1/visits/{id}/photos` - Upload visit photo
- `POST /api/v1/visits/{id}/notes` - Add visit note

## Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Database
DATABASE_URL=postgresql://smart_track:password@localhost:5432/smart_track_db
TEST_DATABASE_URL=postgresql://smart_track:password@localhost:5432/smart_track_test_db

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT Authentication
SECRET_KEY=your-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# CORS
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8081"]

# Environment
ENVIRONMENT=development
DEBUG=true
```

## Docker Services

The project uses Docker Compose to run PostgreSQL and Redis services:

```bash
# Start services
docker-compose up -d postgres redis

# Stop services
docker-compose down

# View service logs
docker-compose logs postgres
docker-compose logs redis

# Check service status
docker-compose ps
```

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Ensure PostgreSQL is running: `docker-compose ps postgres`
   - Check database URL in `.env` file
   - Verify database credentials

2. **Migration errors**
   - Check if database is accessible
   - Ensure all models are imported in `migrations/env.py`
   - Review migration files for conflicts

3. **Import errors**
   - Ensure UV environment is activated: `uv run python`
   - Check if all dependencies are installed: `uv sync`

4. **Permission errors**
   - Check file permissions for upload directory
   - Ensure database user has proper permissions

### Getting Help

1. Check the logs for detailed error messages
2. Verify all prerequisites are installed
3. Ensure Docker services are running
4. Check environment variables configuration

## Contributing

1. Follow the existing code style and patterns
2. Write tests for new functionality
3. Update documentation as needed
4. Use the provided scripts for database management
5. Ensure all tests pass before submitting changes

## License

This project is proprietary software.