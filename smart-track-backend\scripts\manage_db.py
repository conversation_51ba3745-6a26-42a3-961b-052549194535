#!/usr/bin/env python3
"""
Database management script for Smart Track backend
Provides commands for database initialization, migrations, and maintenance
"""

import os
import sys
import argparse
import subprocess
import logging
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import engine
from app.db.init_db import init_database, reset_database
from sqlalchemy import text

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_database_connection():
    """Check if database is accessible"""
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            logger.info("✅ Database connection successful")
            return True
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        logger.error("Make sure PostgreSQL is running and accessible")
        return False


def run_alembic_command(command: str):
    """Run an alembic command"""
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent.parent
        )
        
        if result.returncode == 0:
            if result.stdout:
                print(result.stdout)
            return True
        else:
            if result.stderr:
                print(result.stderr)
            return False
    except Exception as e:
        logger.error(f"Error running command: {e}")
        return False


def init_db(include_sample_data=True):
    """Initialize database with tables and data"""
    if not check_database_connection():
        return False
    
    try:
        logger.info("Initializing database...")
        init_database(include_sample_data=include_sample_data)
        logger.info("✅ Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False


def reset_db():
    """Reset database (drop and recreate all tables)"""
    if not check_database_connection():
        return False
    
    try:
        logger.warning("⚠️  This will delete all data!")
        confirm = input("Are you sure? (yes/no): ")
        if confirm.lower() != 'yes':
            logger.info("Operation cancelled")
            return False
        
        reset_database()
        logger.info("✅ Database reset successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Database reset failed: {e}")
        return False


def create_migration(message):
    """Create a new migration"""
    if not check_database_connection():
        return False
    
    command = f"alembic revision --autogenerate -m '{message}'"
    logger.info(f"Creating migration: {message}")
    return run_alembic_command(command)


def upgrade_db(revision="head"):
    """Apply migrations"""
    if not check_database_connection():
        return False
    
    command = f"alembic upgrade {revision}"
    logger.info(f"Upgrading database to {revision}")
    return run_alembic_command(command)


def downgrade_db(revision):
    """Rollback migrations"""
    if not check_database_connection():
        return False
    
    command = f"alembic downgrade {revision}"
    logger.info(f"Downgrading database to {revision}")
    return run_alembic_command(command)


def show_migration_history():
    """Show migration history"""
    command = "alembic history"
    logger.info("Migration history:")
    return run_alembic_command(command)


def show_current_revision():
    """Show current database revision"""
    command = "alembic current"
    logger.info("Current database revision:")
    return run_alembic_command(command)


def main():
    parser = argparse.ArgumentParser(description="Smart Track Database Management")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Init command
    init_parser = subparsers.add_parser('init', help='Initialize database')
    init_parser.add_argument('--no-sample-data', action='store_true', 
                           help='Skip sample data creation')
    
    # Reset command
    subparsers.add_parser('reset', help='Reset database (WARNING: deletes all data)')
    
    # Migration commands
    migrate_parser = subparsers.add_parser('migrate', help='Create new migration')
    migrate_parser.add_argument('message', help='Migration message')
    
    upgrade_parser = subparsers.add_parser('upgrade', help='Apply migrations')
    upgrade_parser.add_argument('revision', nargs='?', default='head', 
                               help='Target revision (default: head)')
    
    downgrade_parser = subparsers.add_parser('downgrade', help='Rollback migrations')
    downgrade_parser.add_argument('revision', help='Target revision')
    
    # Info commands
    subparsers.add_parser('history', help='Show migration history')
    subparsers.add_parser('current', help='Show current revision')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    success = False
    
    if args.command == 'init':
        success = init_db(include_sample_data=not args.no_sample_data)
    elif args.command == 'reset':
        success = reset_db()
    elif args.command == 'migrate':
        success = create_migration(args.message)
    elif args.command == 'upgrade':
        success = upgrade_db(args.revision)
    elif args.command == 'downgrade':
        success = downgrade_db(args.revision)
    elif args.command == 'history':
        success = show_migration_history()
    elif args.command == 'current':
        success = show_current_revision()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
