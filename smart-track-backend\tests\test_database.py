"""
Test database setup and basic functionality
"""
import pytest
from sqlalchemy import text
from app.core.database import engine, SessionLocal
from app.models import User, Role, Customer, Visit


def test_database_connection():
    """Test that we can connect to the database"""
    with engine.connect() as connection:
        result = connection.execute(text("SELECT 1"))
        assert result.fetchone()[0] == 1


def test_database_tables_exist():
    """Test that all expected tables exist"""
    with engine.connect() as connection:
        result = connection.execute(text("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """))
        
        tables = [row[0] for row in result]
        
        expected_tables = [
            'users', 'roles', 'permissions', 'role_permissions', 'user_profiles',
            'customers', 'customer_contacts', 'territories', 'regions', 'branches',
            'visits', 'visit_photos', 'visit_notes', 'visit_routes'
        ]
        
        for table in expected_tables:
            assert table in tables, f"Table {table} not found in database"


def test_basic_model_operations():
    """Test basic CRUD operations on models"""
    db = SessionLocal()
    try:
        # Test that we can query models without errors
        users_count = db.query(User).count()
        roles_count = db.query(Role).count()
        customers_count = db.query(Customer).count()
        visits_count = db.query(Visit).count()
        
        # These should not raise exceptions
        assert users_count >= 0
        assert roles_count >= 0
        assert customers_count >= 0
        assert visits_count >= 0
        
    finally:
        db.close()


def test_sample_data_exists():
    """Test that sample data was created during initialization"""
    db = SessionLocal()
    try:
        # Check that we have some basic roles
        admin_role = db.query(Role).filter(Role.name == "administrator").first()
        assert admin_role is not None, "Administrator role should exist"
        
        # Check that we have at least one user
        users_count = db.query(User).count()
        assert users_count > 0, "Should have at least one user"
        
    finally:
        db.close()
