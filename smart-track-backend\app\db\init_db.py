"""
Professional database initialization and seeding for Smart Track
This module provides a comprehensive database initialization system following FastAPI best practices.
"""
import logging
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from passlib.context import CryptContext
from datetime import datetime

from app.core.database import SessionLocal, engine
from app.models import Base
from app.models.user import User, Role, Permission, RolePermission, UserProfile
from app.models.organization import Region, Branch, Territory
from app.models.customer import Customer, CustomerContact
from app.models.visit import Visit

logger = logging.getLogger(__name__)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_tables():
    """Create all database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise

def init_permissions(db: Session) -> dict:
    """Initialize system permissions"""
    permissions_data = [
        # User Management
        {"name": "users:read", "description": "View users", "resource": "users", "action": "read"},
        {"name": "users:write", "description": "Create/update users", "resource": "users", "action": "write"},
        {"name": "users:delete", "description": "Delete users", "resource": "users", "action": "delete"},
        
        # Visit Management
        {"name": "visits:read", "description": "View visits", "resource": "visits", "action": "read"},
        {"name": "visits:write", "description": "Create/update visits", "resource": "visits", "action": "write"},
        {"name": "visits:manage", "description": "Manage all visits", "resource": "visits", "action": "manage"},
        
        # Customer Management
        {"name": "customers:read", "description": "View customers", "resource": "customers", "action": "read"},
        {"name": "customers:write", "description": "Create/update customers", "resource": "customers", "action": "write"},
        
        # Analytics
        {"name": "analytics:read", "description": "View analytics", "resource": "analytics", "action": "read"},
    ]
    
    permissions = {}
    for perm_data in permissions_data:
        permission = db.query(Permission).filter(Permission.name == perm_data["name"]).first()
        if not permission:
            permission = Permission(**perm_data)
            db.add(permission)
            db.flush()
        permissions[perm_data["name"]] = permission
    
    return permissions

def init_roles(db: Session, permissions: dict) -> dict:
    """Initialize system roles with permissions"""
    roles_data = {
        "administrator": {
            "description": "System administrator with full access",
            "permissions": list(permissions.keys())  # All permissions
        },
        "sales_manager": {
            "description": "Sales manager with team oversight",
            "permissions": [
                "users:read", "visits:manage", "customers:read", "customers:write", 
                "analytics:read"
            ]
        },
        "sales_representative": {
            "description": "Field sales representative",
            "permissions": [
                "visits:read", "visits:write", "customers:read"
            ]
        }
    }
    
    roles = {}
    for role_name, role_info in roles_data.items():
        role = db.query(Role).filter(Role.name == role_name).first()
        if not role:
            role = Role(name=role_name, description=role_info["description"])
            db.add(role)
            db.flush()
        
        # Assign permissions to role
        for perm_name in role_info["permissions"]:
            if perm_name in permissions:
                role_perm = db.query(RolePermission).filter(
                    RolePermission.role_id == role.id,
                    RolePermission.permission_id == permissions[perm_name].id
                ).first()
                if not role_perm:
                    role_perm = RolePermission(
                        role_id=role.id,
                        permission_id=permissions[perm_name].id
                    )
                    db.add(role_perm)
        
        roles[role_name] = role
    
    return roles

def init_organizational_structure(db: Session) -> Dict[str, Any]:
    """Initialize organizational structure (regions, branches, territories)"""
    logger.info("Initializing organizational structure...")

    # Create default region
    region = db.query(Region).filter(Region.code == "REG001").first()
    if not region:
        region = Region(
            name="Central Region",
            code="REG001",
            description="Main central region",
            is_active=True
        )
        db.add(region)
        db.flush()  # Get the ID

    # Create default branch
    branch = db.query(Branch).filter(Branch.code == "BR001").first()
    if not branch:
        branch = Branch(
            name="Main Branch",
            code="BR001",
            address="123 Business Street",
            city="Business City",
            state="Business State",
            postal_code="12345",
            phone="******-0100",
            is_active=True
        )
        db.add(branch)
        db.flush()

    # Create default territory
    territory = db.query(Territory).filter(Territory.code == "TER001").first()
    if not territory:
        territory = Territory(
            name="Downtown Territory",
            code="TER001",
            description="Downtown business district",
            region_id=region.id,
            is_active=True
        )
        db.add(territory)
        db.flush()

    logger.info("Organizational structure initialized successfully")
    return {"region": region, "branch": branch, "territory": territory}


def init_sample_data(db: Session, roles: dict, org_structure: dict):
    """Initialize sample data for development"""
    logger.info("Initializing sample data...")

    # Create sample users with complete profiles
    sample_users = [
        {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "full_name": "System Administrator",
            "employee_id": "EMP001",
            "phone": "******-0101",
            "role": "administrator"
        },
        {
            "username": "sales_mgr",
            "email": "<EMAIL>",
            "password": "manager123",
            "full_name": "Sales Manager",
            "employee_id": "EMP002",
            "phone": "******-0102",
            "role": "sales_manager"
        },
        {
            "username": "sales_rep",
            "email": "<EMAIL>",
            "password": "rep123",
            "full_name": "Sales Representative",
            "employee_id": "EMP003",
            "phone": "******-0103",
            "role": "sales_representative"
        }
    ]

    for user_data in sample_users:
        existing_user = db.query(User).filter(User.username == user_data["username"]).first()
        if not existing_user:
            hashed_password = pwd_context.hash(user_data["password"])
            user = User(
                username=user_data["username"],
                email=user_data["email"],
                hashed_password=hashed_password,
                full_name=user_data["full_name"],
                employee_id=user_data["employee_id"],
                phone=user_data["phone"],
                role_id=roles[user_data["role"]].id,
                is_active=True,
                is_verified=True
            )
            db.add(user)
            db.flush()

            # Create user profile
            user_profile = UserProfile(
                user_id=user.id,
                territory_id=org_structure["territory"].id,
                branch_id=org_structure["branch"].id,
                department="Sales",
                designation=user_data["role"].replace("_", " ").title(),
                joining_date=datetime.now()
            )
            db.add(user_profile)

    logger.info("Sample data initialized successfully")

def init_sample_customers(db: Session, org_structure: dict):
    """Initialize sample customers for development"""
    logger.info("Initializing sample customers...")

    sample_customers = [
        {
            "code": "CUST001",
            "name": "ABC Electronics Store",
            "address": "456 Commerce Ave",
            "city": "Business City",
            "state": "Business State",
            "postal_code": "12346",
            "phone": "******-0201",
            "email": "<EMAIL>",
            "customer_type": "retailer",
            "credit_limit": 50000.00,
            "latitude": 40.7128,
            "longitude": -74.0060
        },
        {
            "code": "CUST002",
            "name": "XYZ Distribution Center",
            "address": "789 Industrial Blvd",
            "city": "Business City",
            "state": "Business State",
            "postal_code": "12347",
            "phone": "******-0202",
            "email": "<EMAIL>",
            "customer_type": "distributor",
            "credit_limit": 100000.00,
            "latitude": 40.7589,
            "longitude": -73.9851
        }
    ]

    for customer_data in sample_customers:
        existing_customer = db.query(Customer).filter(Customer.code == customer_data["code"]).first()
        if not existing_customer:
            customer = Customer(
                code=customer_data["code"],
                name=customer_data["name"],
                address=customer_data["address"],
                city=customer_data["city"],
                state=customer_data["state"],
                postal_code=customer_data["postal_code"],
                phone=customer_data["phone"],
                email=customer_data["email"],
                customer_type=customer_data["customer_type"],
                credit_limit=customer_data["credit_limit"],
                latitude=customer_data["latitude"],
                longitude=customer_data["longitude"],
                territory_id=org_structure["territory"].id,
                is_active=True
            )
            db.add(customer)

    logger.info("Sample customers initialized successfully")


def init_database(include_sample_data: bool = True):
    """
    Main database initialization function

    Args:
        include_sample_data: Whether to include sample data for development
    """
    try:
        logger.info("Starting database initialization...")

        # Create tables
        create_tables()

        # Initialize data
        db = SessionLocal()
        try:
            # Initialize permissions and roles
            permissions = init_permissions(db)
            roles = init_roles(db, permissions)

            # Initialize organizational structure
            org_structure = init_organizational_structure(db)

            if include_sample_data:
                # Initialize sample data for development
                init_sample_data(db, roles, org_structure)
                init_sample_customers(db, org_structure)

            db.commit()
            logger.info("Database initialization completed successfully")

        except Exception as e:
            db.rollback()
            logger.error(f"Error during database initialization: {e}")
            raise
        finally:
            db.close()

    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


def reset_database():
    """Reset database by dropping and recreating all tables"""
    logger.warning("Resetting database - all data will be lost!")
    try:
        Base.metadata.drop_all(bind=engine)
        logger.info("All tables dropped successfully")
        init_database()
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        raise

if __name__ == "__main__":
    import sys
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Support command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "reset":
            reset_database()
        elif sys.argv[1] == "init-only":
            init_database(include_sample_data=False)
        else:
            print("Usage: python init_db.py [reset|init-only]")
            sys.exit(1)
    else:
        init_database()