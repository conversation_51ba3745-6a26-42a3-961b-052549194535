from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, Text, Float, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(20), unique=True, index=True, nullable=False)
    name = Column(String(100), nullable=False)
    address = Column(Text)
    city = Column(String(50))
    state = Column(String(50))
    postal_code = Column(String(10))
    phone = Column(String(15))
    email = Column(String(100))
    customer_type = Column(String(20))  # 'dealer', 'distributor', 'retailer'
    credit_limit = Column(DECIMAL(12, 2), default=0.0)
    current_balance = Column(DECIMAL(12, 2), default=0.0)
    territory_id = Column(Integer, ForeignKey("territories.id"))
    
    # GPS Coordinates for location-based services
    latitude = Column(Float)
    longitude = Column(Float)
    
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    territory = relationship("Territory", back_populates="customers")
    contacts = relationship("CustomerContact", back_populates="customer")
    visits = relationship("Visit", back_populates="customer")

class CustomerContact(Base):
    __tablename__ = "customer_contacts"

    id = Column(Integer, primary_key=True, index=True)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    name = Column(String(100), nullable=False)
    designation = Column(String(50))
    phone = Column(String(15))
    email = Column(String(100))
    is_primary = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    customer = relationship("Customer", back_populates="contacts")


