version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: smart_track_postgres
    environment:
      POSTGRES_USER: smart_track
      POSTGRES_PASSWORD: password
      POSTGRES_DB: smart_track_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - smart_track_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U smart_track -d smart_track_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: smart_track_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - smart_track_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend service commented out for local development
  # Uncomment if you want to run the backend in Docker
  # backend:
  #   build: .
  #   container_name: smart_track_backend
  #   ports:
  #     - "8000:8000"
  #   environment:
  #     - DATABASE_URL=***********************************************/smart_track_db
  #     - REDIS_URL=redis://redis:6379/0
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   volumes:
  #     - ./uploads:/app/uploads
  #     - .:/app
  #   networks:
  #     - smart_track_network
  #   command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_data:
  redis_data:

networks:
  smart_track_network:
    driver: bridge
